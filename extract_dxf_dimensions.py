#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF文件中dimension标注值提取工具
支持提取线性、角度、半径、直径等各种类型的标注
"""

import ezdxf
import os
import json
from typing import List, Dict, Any
import math

class DXFDimensionExtractor:
    """DXF标注提取器"""
    
    def __init__(self, dxf_file_path: str):
        """
        初始化DXF标注提取器
        
        Args:
            dxf_file_path: DXF文件路径
        """
        self.dxf_file_path = dxf_file_path
        self.doc = None
        self.dimensions = []
        
    def load_dxf(self) -> bool:
        """
        加载DXF文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            self.doc = ezdxf.readfile(self.dxf_file_path)
            print(f"成功加载DXF文件: {self.dxf_file_path}")
            return True
        except Exception as e:
            print(f"加载DXF文件失败: {e}")
            return False
    
    def extract_dimensions(self) -> List[Dict[str, Any]]:
        """
        提取所有dimension标注
        
        Returns:
            List[Dict]: 标注信息列表
        """
        if not self.doc:
            print("请先加载DXF文件")
            return []
        
        self.dimensions = []
        
        # 遍历所有图层
        for layout in self.doc.layouts:
            print(f"正在处理图层: {layout.name}")
            
            # 查找所有DIMENSION实体
            for entity in layout.query('DIMENSION'):
                dim_info = self._extract_dimension_info(entity)
                if dim_info:
                    self.dimensions.append(dim_info)
        
        print(f"共提取到 {len(self.dimensions)} 个标注")
        return self.dimensions
    
    def _extract_dimension_info(self, dim_entity) -> Dict[str, Any]:
        """
        提取单个dimension实体的信息
        
        Args:
            dim_entity: dimension实体对象
            
        Returns:
            Dict: 标注信息字典
        """
        try:
            dim_info = {
                'handle': dim_entity.dxf.handle,
                'layer': dim_entity.dxf.layer,
                'dimension_type': self._get_dimension_type(dim_entity),
                'measurement': None,
                'text': None,
                'position': None,
                'definition_points': [],
                'style': getattr(dim_entity.dxf, 'dimstyle', 'STANDARD')
            }
            
            # 获取标注值
            if hasattr(dim_entity.dxf, 'measurement'):
                dim_info['measurement'] = dim_entity.dxf.measurement
            
            # 获取标注文本
            if hasattr(dim_entity.dxf, 'text'):
                dim_info['text'] = dim_entity.dxf.text
            
            # 获取标注位置
            if hasattr(dim_entity.dxf, 'text_midpoint'):
                pos = dim_entity.dxf.text_midpoint
                dim_info['position'] = {'x': pos.x, 'y': pos.y, 'z': pos.z}
            
            # 获取定义点
            self._extract_definition_points(dim_entity, dim_info)
            
            # 计算实际标注值
            dim_info['calculated_value'] = self._calculate_dimension_value(dim_entity, dim_info)

            # 改进标注类型识别
            dim_info['inferred_type'] = self._infer_dimension_type(dim_info)

            return dim_info
            
        except Exception as e:
            print(f"提取标注信息时出错: {e}")
            return None
    
    def _get_dimension_type(self, dim_entity) -> str:
        """获取标注类型"""
        dim_type = getattr(dim_entity.dxf, 'dimtype', 0)
        
        type_mapping = {
            0: 'LINEAR',      # 线性标注
            1: 'ALIGNED',     # 对齐标注
            2: 'ANGULAR',     # 角度标注
            3: 'DIAMETER',    # 直径标注
            4: 'RADIUS',      # 半径标注
            5: 'ANGULAR_3P',  # 三点角度标注
            6: 'ORDINATE'     # 坐标标注
        }
        
        return type_mapping.get(dim_type, f'UNKNOWN_{dim_type}')
    
    def _extract_definition_points(self, dim_entity, dim_info: Dict):
        """提取标注定义点"""
        try:
            # 标注定义点
            points = []
            
            if hasattr(dim_entity.dxf, 'defpoint'):
                points.append({
                    'name': 'defpoint',
                    'coordinates': {
                        'x': dim_entity.dxf.defpoint.x,
                        'y': dim_entity.dxf.defpoint.y,
                        'z': dim_entity.dxf.defpoint.z
                    }
                })
            
            if hasattr(dim_entity.dxf, 'defpoint2'):
                points.append({
                    'name': 'defpoint2',
                    'coordinates': {
                        'x': dim_entity.dxf.defpoint2.x,
                        'y': dim_entity.dxf.defpoint2.y,
                        'z': dim_entity.dxf.defpoint2.z
                    }
                })
            
            if hasattr(dim_entity.dxf, 'defpoint3'):
                points.append({
                    'name': 'defpoint3',
                    'coordinates': {
                        'x': dim_entity.dxf.defpoint3.x,
                        'y': dim_entity.dxf.defpoint3.y,
                        'z': dim_entity.dxf.defpoint3.z
                    }
                })
            
            dim_info['definition_points'] = points
            
        except Exception as e:
            print(f"提取定义点时出错: {e}")
    
    def _calculate_dimension_value(self, dim_entity, dim_info: Dict) -> float:
        """计算标注的实际值"""
        try:
            # 如果有measurement属性，直接使用
            if dim_info['measurement'] is not None:
                return dim_info['measurement']

            # 根据标注类型和定义点计算
            dim_type = dim_info['dimension_type']
            points = dim_info['definition_points']
            text = dim_info.get('text', '')

            # 对于UNKNOWN_160类型，根据文本和定义点推断标注类型
            if dim_type == 'UNKNOWN_160' and len(points) >= 2:
                # 检查是否为直径标注（文本包含%%c表示直径符号）
                if '%%c' in text:
                    # 直径标注：计算两个定义点间的距离作为直径
                    if len(points) >= 3:
                        # 使用defpoint和defpoint2计算直径
                        p1 = points[0]['coordinates']  # defpoint
                        p2 = points[1]['coordinates']  # defpoint2
                        diameter = math.sqrt(
                            (p2['x'] - p1['x'])**2 +
                            (p2['y'] - p1['y'])**2
                        )
                        return diameter
                else:
                    # 线性标注：计算两个主要定义点间的距离
                    if len(points) >= 2:
                        p1 = points[0]['coordinates']  # defpoint
                        p2 = points[1]['coordinates']  # defpoint2
                        distance = math.sqrt(
                            (p2['x'] - p1['x'])**2 +
                            (p2['y'] - p1['y'])**2 +
                            (p2['z'] - p1['z'])**2
                        )
                        return distance

            elif dim_type in ['LINEAR', 'ALIGNED'] and len(points) >= 2:
                # 计算两点间距离
                p1 = points[0]['coordinates']
                p2 = points[1]['coordinates']
                distance = math.sqrt(
                    (p2['x'] - p1['x'])**2 +
                    (p2['y'] - p1['y'])**2 +
                    (p2['z'] - p1['z'])**2
                )
                return distance

            elif dim_type == 'RADIUS' and len(points) >= 2:
                # 半径标注
                center = points[0]['coordinates']
                point = points[1]['coordinates']
                radius = math.sqrt(
                    (point['x'] - center['x'])**2 +
                    (point['y'] - center['y'])**2
                )
                return radius

            elif dim_type == 'DIAMETER' and len(points) >= 2:
                # 直径标注
                center = points[0]['coordinates']
                point = points[1]['coordinates']
                radius = math.sqrt(
                    (point['x'] - center['x'])**2 +
                    (point['y'] - center['y'])**2
                )
                return radius * 2

            return None

        except Exception as e:
            print(f"计算标注值时出错: {e}")
            return None

    def _infer_dimension_type(self, dim_info: Dict) -> str:
        """根据文本和几何信息推断标注类型"""
        try:
            text = dim_info.get('text', '')
            points = dim_info['definition_points']

            # 检查直径标注
            if '%%c' in text:
                return 'DIAMETER'

            # 检查半径标注
            if 'R' in text or 'r' in text:
                return 'RADIUS'

            # 检查角度标注
            if '°' in text or 'deg' in text.lower():
                return 'ANGULAR'

            # 根据几何特征判断
            if len(points) >= 2:
                p1 = points[0]['coordinates']
                p2 = points[1]['coordinates']

                # 如果两点在同一水平线或垂直线上，可能是线性标注
                dx = abs(p2['x'] - p1['x'])
                dy = abs(p2['y'] - p1['y'])

                if dx < 0.001:  # 垂直线
                    return 'VERTICAL_LINEAR'
                elif dy < 0.001:  # 水平线
                    return 'HORIZONTAL_LINEAR'
                else:
                    return 'ALIGNED_LINEAR'

            return 'LINEAR'

        except Exception as e:
            print(f"推断标注类型时出错: {e}")
            return 'UNKNOWN'
    
    def save_to_json(self, output_file: str = "dimensions.json"):
        """
        将提取的标注信息保存为JSON文件
        
        Args:
            output_file: 输出文件名
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.dimensions, f, ensure_ascii=False, indent=2)
            print(f"标注信息已保存到: {output_file}")
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
    
    def print_summary(self):
        """打印标注信息摘要"""
        if not self.dimensions:
            print("没有找到标注信息")
            return
        
        print("\n=== 标注信息摘要 ===")
        print(f"总标注数量: {len(self.dimensions)}")
        
        # 按类型统计
        type_count = {}
        for dim in self.dimensions:
            dim_type = dim['dimension_type']
            type_count[dim_type] = type_count.get(dim_type, 0) + 1
        
        print("\n按类型统计:")
        for dim_type, count in type_count.items():
            print(f"  {dim_type}: {count}")
        
        print("\n详细信息:")
        for i, dim in enumerate(self.dimensions, 1):
            print(f"\n标注 {i}:")
            print(f"  原始类型: {dim['dimension_type']}")
            print(f"  推断类型: {dim.get('inferred_type', 'UNKNOWN')}")
            print(f"  图层: {dim['layer']}")
            print(f"  测量值: {dim['measurement']}")
            print(f"  计算值: {dim['calculated_value']:.2f}" if dim['calculated_value'] else "  计算值: None")
            print(f"  文本: {dim['text']}")
            if dim['position']:
                pos = dim['position']
                print(f"  位置: ({pos['x']:.2f}, {pos['y']:.2f}, {pos['z']:.2f})")

            # 显示定义点信息
            if dim['definition_points']:
                print(f"  定义点数量: {len(dim['definition_points'])}")
                for j, point in enumerate(dim['definition_points'][:2]):  # 只显示前两个点
                    coords = point['coordinates']
                    print(f"    {point['name']}: ({coords['x']:.2f}, {coords['y']:.2f})")


def main():
    """主函数"""
    # DXF文件路径
    dxf_file = "dxf.DXF"
    
    # 检查文件是否存在
    if not os.path.exists(dxf_file):
        print(f"错误: 找不到文件 {dxf_file}")
        return
    
    # 创建提取器实例
    extractor = DXFDimensionExtractor(dxf_file)
    
    # 加载DXF文件
    if not extractor.load_dxf():
        return
    
    # 提取标注信息
    dimensions = extractor.extract_dimensions()
    
    # 打印摘要
    extractor.print_summary()
    
    # 保存到JSON文件
    extractor.save_to_json("extracted_dimensions.json")
    
    print(f"\n处理完成！共提取到 {len(dimensions)} 个标注")


if __name__ == "__main__":
    main()
