#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装DXF/DWG处理所需的依赖库
"""

import subprocess
import sys


def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False


def main():
    """主函数"""
    print("DXF/DWG处理依赖库安装程序")
    print("=" * 40)
    
    # 必需的包
    required_packages = [
        "ezdxf",  # DXF文件处理的主要库
    ]
    
    # 可选的包（用于DWG支持）
    optional_packages = [
        # "ezdxf[odafc]",  # ODA文件转换器支持（需要额外配置）
    ]
    
    print("安装必需的依赖库...")
    success_count = 0
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(required_packages)} 个必需包安装成功")
    
    if success_count == len(required_packages):
        print("✅ 所有必需依赖已安装，可以开始使用DXF文件处理功能")
        
        # 测试导入
        try:
            import ezdxf
            print(f"✅ ezdxf版本: {ezdxf.version}")
        except ImportError:
            print("❌ ezdxf导入失败")
    else:
        print("❌ 部分依赖安装失败，请检查网络连接或手动安装")
    
    print("\n注意事项:")
    print("- DXF文件可以直接处理")
    print("- DWG文件需要额外的ODA文件转换器支持")
    print("- 如需处理DWG文件，请参考ezdxf官方文档配置ODA转换器")


if __name__ == "__main__":
    main()
