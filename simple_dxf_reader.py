#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版DXF标注读取器
快速提取和显示DXF文件中的标注信息
"""

import os

def check_dxf_file(file_path):
    """检查DXF文件基本信息"""
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return False
    
    file_size = os.path.getsize(file_path)
    print(f"DXF文件信息:")
    print(f"  文件路径: {file_path}")
    print(f"  文件大小: {file_size:,} 字节")
    
    return True

def read_dxf_with_ezdxf(file_path):
    """使用ezdxf库读取DXF文件"""
    try:
        import ezdxf
        print("\n使用ezdxf库解析DXF文件...")
        
        # 读取DXF文件
        doc = ezdxf.readfile(file_path)
        print(f"DXF版本: {doc.dxfversion}")
        
        # 统计信息
        total_dimensions = 0
        
        print(f"\n图层信息:")
        for layout in doc.layouts:
            print(f"  图层: {layout.name}")
            
            # 查找标注实体
            dimensions = list(layout.query('DIMENSION'))
            total_dimensions += len(dimensions)
            
            if dimensions:
                print(f"    找到 {len(dimensions)} 个标注")
                
                # 显示前几个标注的详细信息
                for i, dim in enumerate(dimensions[:3]):  # 只显示前3个
                    print(f"    标注 {i+1}:")
                    
                    # 标注类型
                    dim_type = getattr(dim.dxf, 'dimtype', 'unknown')
                    type_names = {0: '线性', 1: '对齐', 2: '角度', 3: '直径', 4: '半径'}
                    type_name = type_names.get(dim_type, f'类型{dim_type}')
                    print(f"      类型: {type_name}")
                    
                    # 测量值
                    if hasattr(dim.dxf, 'measurement'):
                        print(f"      测量值: {dim.dxf.measurement}")
                    
                    # 文本
                    if hasattr(dim.dxf, 'text'):
                        print(f"      文本: {dim.dxf.text}")
                    
                    # 图层
                    print(f"      图层: {dim.dxf.layer}")
                
                if len(dimensions) > 3:
                    print(f"    ... 还有 {len(dimensions) - 3} 个标注")
            else:
                print(f"    未找到标注")
        
        print(f"\n总计找到 {total_dimensions} 个标注")
        return True
        
    except ImportError:
        print("错误: 未安装ezdxf库")
        print("请运行: python install_dxf_dependencies.py")
        return False
    except Exception as e:
        print(f"读取DXF文件时出错: {e}")
        return False

def read_dxf_raw(file_path):
    """原始方式读取DXF文件（查找标注相关的文本）"""
    print("\n使用原始文本方式分析DXF文件...")
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 查找可能的标注相关内容
        lines = content.split('\n')
        
        dimension_indicators = ['DIMENSION', 'DIMSTYLE', 'MTEXT', 'TEXT']
        found_items = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if line in dimension_indicators:
                # 收集相关行
                context = []
                start = max(0, i-2)
                end = min(len(lines), i+10)
                for j in range(start, end):
                    context.append(f"{j:4d}: {lines[j].strip()}")
                found_items.append((line, i, context))
        
        if found_items:
            print(f"找到 {len(found_items)} 个可能的标注相关项目:")
            for item_type, line_num, context in found_items[:5]:  # 只显示前5个
                print(f"\n  {item_type} (行 {line_num}):")
                for ctx_line in context:
                    print(f"    {ctx_line}")
        else:
            print("未找到明显的标注相关内容")
        
        return True
        
    except Exception as e:
        print(f"原始读取DXF文件时出错: {e}")
        return False

def main():
    """主函数"""
    print("=== DXF标注信息快速查看器 ===")
    
    # DXF文件路径
    dxf_file = "dxf.DXF"
    
    # 检查文件
    if not check_dxf_file(dxf_file):
        return
    
    # 尝试使用ezdxf库读取
    if not read_dxf_with_ezdxf(dxf_file):
        # 如果ezdxf不可用，使用原始方式
        read_dxf_raw(dxf_file)
    
    print("\n=== 使用说明 ===")
    print("1. 如果看到'未安装ezdxf库'的错误，请先运行:")
    print("   python install_dxf_dependencies.py")
    print("2. 然后运行完整版本:")
    print("   python extract_dxf_dimensions.py")

if __name__ == "__main__":
    main()
