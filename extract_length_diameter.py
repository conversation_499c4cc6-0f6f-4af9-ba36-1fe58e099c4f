#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版DXF标注提取器 - 只提取长度和直径标注
"""

import ezdxf
import os
import json
import math
from typing import List, Dict, Any

class SimpleDimensionExtractor:
    """简化的标注提取器，只关注长度和直径"""
    
    def __init__(self, dxf_file_path: str):
        self.dxf_file_path = dxf_file_path
        self.doc = None
        self.length_dimensions = []
        self.diameter_dimensions = []
        
    def load_dxf(self) -> bool:
        """加载DXF文件"""
        try:
            self.doc = ezdxf.readfile(self.dxf_file_path)
            print(f"成功加载DXF文件: {self.dxf_file_path}")
            return True
        except Exception as e:
            print(f"加载DXF文件失败: {e}")
            return False
    
    def extract_dimensions(self):
        """提取长度和直径标注"""
        if not self.doc:
            print("请先加载DXF文件")
            return
        
        self.length_dimensions = []
        self.diameter_dimensions = []
        
        # 遍历所有图层
        for layout in self.doc.layouts:
            for entity in layout.query('DIMENSION'):
                dim_info = self._process_dimension(entity)
                if dim_info:
                    if dim_info['type'] == 'LENGTH':
                        self.length_dimensions.append(dim_info)
                    elif dim_info['type'] == 'DIAMETER':
                        self.diameter_dimensions.append(dim_info)
        
        print(f"提取完成:")
        print(f"  长度标注: {len(self.length_dimensions)} 个")
        print(f"  直径标注: {len(self.diameter_dimensions)} 个")
    
    def _process_dimension(self, dim_entity) -> Dict[str, Any]:
        """处理单个标注实体"""
        try:
            # 获取基本信息
            text = getattr(dim_entity.dxf, 'text', '')
            layer = getattr(dim_entity.dxf, 'layer', '')
            
            # 获取定义点
            points = self._get_definition_points(dim_entity)
            if len(points) < 2:
                return None
            
            # 判断是直径还是长度
            if '%%c' in text:  # %%c 是直径符号
                dim_type = 'DIAMETER'
                value = self._calculate_diameter(points)
            else:
                dim_type = 'LENGTH'
                value = self._calculate_length(points)
            
            if value is None:
                return None
            
            # 获取位置
            position = None
            if hasattr(dim_entity.dxf, 'text_midpoint'):
                pos = dim_entity.dxf.text_midpoint
                position = {'x': pos.x, 'y': pos.y}
            
            return {
                'type': dim_type,
                'value': round(value, 2),
                'text': text,
                'layer': layer,
                'position': position,
                'points': points
            }
            
        except Exception as e:
            print(f"处理标注时出错: {e}")
            return None
    
    def _get_definition_points(self, dim_entity) -> List[Dict]:
        """获取标注定义点"""
        points = []
        try:
            if hasattr(dim_entity.dxf, 'defpoint'):
                points.append({
                    'x': dim_entity.dxf.defpoint.x,
                    'y': dim_entity.dxf.defpoint.y
                })
            
            if hasattr(dim_entity.dxf, 'defpoint2'):
                points.append({
                    'x': dim_entity.dxf.defpoint2.x,
                    'y': dim_entity.dxf.defpoint2.y
                })
            
            if hasattr(dim_entity.dxf, 'defpoint3'):
                points.append({
                    'x': dim_entity.dxf.defpoint3.x,
                    'y': dim_entity.dxf.defpoint3.y
                })
        except Exception as e:
            print(f"获取定义点时出错: {e}")
        
        return points
    
    def _calculate_length(self, points: List[Dict]) -> float:
        """计算长度标注的值"""
        if len(points) < 2:
            return None
        
        p1 = points[0]
        p2 = points[1]
        
        distance = math.sqrt((p2['x'] - p1['x'])**2 + (p2['y'] - p1['y'])**2)
        return distance
    
    def _calculate_diameter(self, points: List[Dict]) -> float:
        """计算直径标注的值"""
        if len(points) < 2:
            return None
        
        # 对于直径标注，通常是圆心到圆周的距离的2倍
        p1 = points[0]
        p2 = points[1]
        
        radius = math.sqrt((p2['x'] - p1['x'])**2 + (p2['y'] - p1['y'])**2)
        diameter = radius * 2
        
        return diameter
    
    def print_results(self):
        """打印提取结果"""
        print("\n=== 长度标注 ===")
        if self.length_dimensions:
            for i, dim in enumerate(self.length_dimensions, 1):
                print(f"{i}. 长度: {dim['value']} mm")
                print(f"   图层: {dim['layer']}")
                if dim['position']:
                    print(f"   位置: ({dim['position']['x']:.1f}, {dim['position']['y']:.1f})")
                print()
        else:
            print("未找到长度标注")
        
        print("\n=== 直径标注 ===")
        if self.diameter_dimensions:
            for i, dim in enumerate(self.diameter_dimensions, 1):
                print(f"{i}. 直径: {dim['value']} mm")
                print(f"   图层: {dim['layer']}")
                if dim['position']:
                    print(f"   位置: ({dim['position']['x']:.1f}, {dim['position']['y']:.1f})")
                print()
        else:
            print("未找到直径标注")
    
    def get_summary(self) -> Dict:
        """获取摘要信息"""
        length_values = [dim['value'] for dim in self.length_dimensions]
        diameter_values = [dim['value'] for dim in self.diameter_dimensions]
        
        summary = {
            'length_count': len(length_values),
            'diameter_count': len(diameter_values),
            'length_values': length_values,
            'diameter_values': diameter_values
        }
        
        if length_values:
            summary['length_stats'] = {
                'min': min(length_values),
                'max': max(length_values),
                'avg': sum(length_values) / len(length_values)
            }
        
        if diameter_values:
            summary['diameter_stats'] = {
                'min': min(diameter_values),
                'max': max(diameter_values),
                'avg': sum(diameter_values) / len(diameter_values)
            }
        
        return summary
    
    def save_results(self, filename: str = "dimensions_simple.json"):
        """保存结果到JSON文件"""
        results = {
            'length_dimensions': self.length_dimensions,
            'diameter_dimensions': self.diameter_dimensions,
            'summary': self.get_summary()
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {filename}")
        except Exception as e:
            print(f"保存文件失败: {e}")


def main():
    """主函数"""
    print("=== DXF长度和直径标注提取器 ===")
    
    # DXF文件路径
    dxf_file = "dxf.DXF"
    
    # 检查文件是否存在
    if not os.path.exists(dxf_file):
        print(f"错误: 找不到文件 {dxf_file}")
        return
    
    # 创建提取器
    extractor = SimpleDimensionExtractor(dxf_file)
    
    # 加载和处理
    if extractor.load_dxf():
        extractor.extract_dimensions()
        extractor.print_results()
        
        # 显示统计信息
        summary = extractor.get_summary()
        print("\n=== 统计信息 ===")
        print(f"长度标注数量: {summary['length_count']}")
        print(f"直径标注数量: {summary['diameter_count']}")
        
        if summary.get('length_stats'):
            stats = summary['length_stats']
            print(f"长度范围: {stats['min']:.2f} - {stats['max']:.2f} mm")
            print(f"平均长度: {stats['avg']:.2f} mm")
        
        if summary.get('diameter_stats'):
            stats = summary['diameter_stats']
            print(f"直径范围: {stats['min']:.2f} - {stats['max']:.2f} mm")
            print(f"平均直径: {stats['avg']:.2f} mm")
        
        # 保存结果
        extractor.save_results()
        
        print(f"\n所有长度值: {summary['length_values']}")
        print(f"所有直径值: {summary['diameter_values']}")


if __name__ == "__main__":
    main()
