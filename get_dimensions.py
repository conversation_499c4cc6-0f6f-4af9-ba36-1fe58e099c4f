#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速获取DXF文件中的长度和直径数值
"""

import ezdxf
import math

def extract_dimensions(dxf_file):
    """提取DXF文件中的长度和直径标注值"""
    try:
        # 加载DXF文件
        doc = ezdxf.readfile(dxf_file)
        
        lengths = []
        diameters = []
        
        # 遍历所有图层查找标注
        for layout in doc.layouts:
            for entity in layout.query('DIMENSION'):
                # 获取文本和定义点
                text = getattr(entity.dxf, 'text', '')
                
                # 获取定义点
                points = []
                if hasattr(entity.dxf, 'defpoint'):
                    points.append((entity.dxf.defpoint.x, entity.dxf.defpoint.y))
                if hasattr(entity.dxf, 'defpoint2'):
                    points.append((entity.dxf.defpoint2.x, entity.dxf.defpoint2.y))
                
                if len(points) >= 2:
                    # 计算两点间距离
                    p1, p2 = points[0], points[1]
                    distance = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
                    
                    # 判断是直径还是长度
                    if '%%c' in text:  # 直径标注
                        diameters.append(round(distance * 2, 2))
                    else:  # 长度标注
                        lengths.append(round(distance, 2))
        
        return lengths, diameters
        
    except Exception as e:
        print(f"处理DXF文件时出错: {e}")
        return [], []

def main():
    """主函数"""
    dxf_file = "dxf.DXF"
    
    lengths, diameters = extract_dimensions(dxf_file)
    
    print("=== 提取结果 ===")
    print(f"长度标注值: {lengths}")
    print(f"直径标注值: {diameters}")
    
    print(f"\n长度数量: {len(lengths)}")
    print(f"直径数量: {len(diameters)}")
    
    if lengths:
        print(f"长度范围: {min(lengths)} - {max(lengths)} mm")
    if diameters:
        print(f"直径范围: {min(diameters)} - {max(diameters)} mm")

if __name__ == "__main__":
    main()
